"""
Generated PDF Recreation Code
Source: norwegian_contract_ringerike.pdf
Generated by PDF Analyzer
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from pdf_engine import PDFEngine


def recreate_pdf():
    """Recreate the analyzed PDF using PDF Engine."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create static document
    doc = engine.create_document("recreated_document.pdf")
    
    doc.add_title("ARBEIDSAVTALE Arbeidsgiver: Ringerike Landskap AS ...")
    doc.add_paragraph("Arbeidstakeren har rett til 5 ukers ferie per år i henhold til ferieloven. Feriepenger utbetales med 12% av opptjent lønn. Dato: ________________________________ Arbeidsgiver: Ringerike Landskap AS __...")
    
    # Render the document
    doc.render_to_file("recreated_document.pdf")
    print("Static document created: recreated_document.pdf")


if __name__ == "__main__":
    recreate_pdf()
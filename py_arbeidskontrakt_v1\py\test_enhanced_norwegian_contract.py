#!/usr/bin/env python3
"""
Test script for enhanced Norwegian employment contract generation.
Uses the improved PDF engine with precise positioning and professional styling.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_engine.core import PDFEngine
from pdf_engine.forms import FormBuilder
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import black
from reportlab.lib.units import mm

def create_enhanced_norwegian_contract():
    """Create an enhanced Norwegian employment contract using the improved system."""
    
    print("Creating enhanced Norwegian employment contract...")
    
    # Initialize the enhanced PDF engine with Norwegian configuration
    config = {
        'page_size': A4,
        'margins': {
            'top': 57,
            'bottom': 57,
            'left': 44,
            'right': 44
        },
        'fonts': {
            'primary': 'Helvetica',  # Would be Calibri in production
            'fallback': 'Helvetica'
        },
        'font_sizes': {
            'title': 16,
            'heading': 9,
            'body': 9,
            'label': 8
        },
        'language': 'nb',
        'document_type': 'norwegian_employment_contract'
    }
    
    engine = PDFEngine(config)
    
    # Create the Norwegian contract form
    output_path = "output/enhanced_norwegian_contract.pdf"
    os.makedirs("output", exist_ok=True)
    
    # Create form using enhanced form builder
    form = engine.create_norwegian_contract_form(output_path)
    
    # Generate the PDF
    form.render_to_file(output_path)
    
    print(f"Enhanced Norwegian contract generated: {output_path}")
    return output_path

def create_complete_norwegian_contract():
    """Create a complete Norwegian contract with all sections and proper styling."""
    
    print("Creating complete Norwegian employment contract...")
    
    output_path = "output/complete_norwegian_contract.pdf"
    os.makedirs("output", exist_ok=True)
    
    # Create canvas with A4 size
    c = canvas.Canvas(output_path, pagesize=A4)
    width, height = A4
    
    # Set up fonts and colors
    c.setFont("Helvetica-Bold", 16)
    c.setFillColor(black)
    
    # Document header
    header_y = height - 60
    c.drawString(44, header_y, "Standard arbeidsavtale")
    
    c.setFont("Helvetica", 9)
    c.drawString(44, header_y - 20, "bokmål | september 2024")
    c.drawString(44, header_y - 35, "Beholdes av arbeidsgiver – kopi til arbeidstaker")
    
    # Section 1: Arbeidsgiver/virksomhet
    current_y = header_y - 80
    c.setFont("Helvetica-Bold", 9)
    c.drawString(44, current_y, "1. Arbeidsgiver/virksomhet")
    
    current_y -= 25
    c.setFont("Helvetica", 8)
    c.drawString(44, current_y, "Virksomhetens navn:")
    
    # Add form field with underline
    field_y = current_y - 15
    c.acroForm.textfield(
        name="virksomhetens_navn",
        x=44,
        y=field_y,
        width=505,
        height=16,
        value="Ringerike Landskap AS",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    # Add underline
    c.setStrokeColor(black)
    c.setLineWidth(0.5)
    c.line(44, field_y, 549, field_y)
    
    current_y -= 40
    c.drawString(44, current_y, "Organisasjonsnummer:")
    
    field_y = current_y - 15
    c.acroForm.textfield(
        name="organisasjonsnummer",
        x=44,
        y=field_y,
        width=505,
        height=16,
        value="123 456 789",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(44, field_y, 549, field_y)
    
    current_y -= 40
    c.drawString(44, current_y, "Adresse:")
    
    field_y = current_y - 15
    c.acroForm.textfield(
        name="arbeidsgiver_adresse",
        x=44,
        y=field_y,
        width=505,
        height=16,
        value="Hovedgata 123, 3500 Hønefoss",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(44, field_y, 549, field_y)
    
    # Section 2: Arbeidstaker
    current_y -= 60
    c.setFont("Helvetica-Bold", 9)
    c.drawString(44, current_y, "2. Arbeidstaker")
    
    current_y -= 25
    c.setFont("Helvetica", 8)
    c.drawString(44, current_y, "Navn:")
    c.drawString(414, current_y, "Fødselsdato:")
    
    # Split fields for name and birth date
    field_y = current_y - 15
    c.acroForm.textfield(
        name="arbeidstaker_navn",
        x=44,
        y=field_y,
        width=365,
        height=16,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(44, field_y, 409, field_y)
    
    c.acroForm.textfield(
        name="fodselsdato",
        x=414,
        y=field_y,
        width=137,
        height=16,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(414, field_y, 551, field_y)
    
    current_y -= 40
    c.drawString(44, current_y, "Adresse:")
    
    field_y = current_y - 15
    c.acroForm.textfield(
        name="arbeidstaker_adresse",
        x=44,
        y=field_y,
        width=505,
        height=16,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(44, field_y, 549, field_y)
    
    # Section 3: Arbeidsplass
    current_y -= 60
    c.setFont("Helvetica-Bold", 9)
    c.drawString(44, current_y, "3. Arbeidsplass")
    
    current_y -= 25
    c.setFont("Helvetica", 8)
    c.drawString(44, current_y, "Adresse:")
    
    field_y = current_y - 15
    c.acroForm.textfield(
        name="arbeidsplass_adresse",
        x=44,
        y=field_y,
        width=505,
        height=16,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9
    )
    c.line(44, field_y, 549, field_y)
    
    # Section 4: Ansatt som
    current_y -= 60
    c.setFont("Helvetica-Bold", 9)
    c.drawString(44, current_y, "4. Ansatt som")
    
    current_y -= 25
    c.setFont("Helvetica", 8)
    c.drawString(44, current_y, "Tittel/beskrivelse av stillingen:")
    
    field_y = current_y - 15
    c.acroForm.textfield(
        name="ansatt_som",
        x=44,
        y=field_y - 20,
        width=505,
        height=36,
        value="",
        forceBorder=False,
        fontName="Helvetica",
        fontSize=9,
        multiline=True
    )
    # Multi-line field with multiple underlines
    c.line(44, field_y, 549, field_y)
    c.line(44, field_y - 20, 549, field_y - 20)
    
    # Add footer
    footer_y = 40
    c.setFont("Helvetica", 8)
    c.drawString(44, footer_y, "AT-563-NB (September 2024) Side 1 av 3")
    
    # Save the PDF
    c.save()
    
    print(f"Complete Norwegian contract generated: {output_path}")
    return output_path

def main():
    """Main function to test the enhanced Norwegian contract generation."""
    
    print("Testing Enhanced Norwegian Employment Contract Generation")
    print("=" * 60)
    
    try:
        # Test 1: Enhanced form using improved system
        print("\n1. Testing enhanced form system...")
        enhanced_path = create_enhanced_norwegian_contract()
        
        # Test 2: Complete contract with all sections
        print("\n2. Testing complete contract generation...")
        complete_path = create_complete_norwegian_contract()
        
        print("\n" + "=" * 60)
        print("SUCCESS: Both PDFs generated successfully!")
        print(f"Enhanced form: {enhanced_path}")
        print(f"Complete contract: {complete_path}")
        
        # Validate the generated files
        if os.path.exists(enhanced_path):
            size = os.path.getsize(enhanced_path)
            print(f"Enhanced PDF size: {size} bytes")
        
        if os.path.exists(complete_path):
            size = os.path.getsize(complete_path)
            print(f"Complete PDF size: {size} bytes")
            
    except Exception as e:
        print(f"ERROR: Failed to generate PDFs: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

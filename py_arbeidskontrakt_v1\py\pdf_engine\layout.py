"""
Layout Manager Mo<PERSON><PERSON>

Handles advanced layout operations, positioning, and complex document
structures. Provides utilities for responsive layouts and positioning.
"""

from typing import Dict, Any, Tuple, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from reportlab.lib.units import inch, cm, mm
from reportlab.lib.pagesizes import A4, letter

from .exceptions import LayoutError, ValidationError


class Alignment(Enum):
    """Text and element alignment options."""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"
    JUSTIFY = "justify"


class Units(Enum):
    """Measurement units."""
    POINTS = "points"
    INCHES = "inches"
    CM = "cm"
    MM = "mm"


@dataclass
class Position:
    """Position coordinates with units."""
    x: float
    y: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Tuple[float, float]:
        """Convert position to points."""
        if self.unit == Units.POINTS:
            return (self.x, self.y)
        elif self.unit == Units.INCHES:
            return (self.x * inch, self.y * inch)
        elif self.unit == Units.CM:
            return (self.x * cm, self.y * cm)
        elif self.unit == Units.MM:
            return (self.x * mm, self.y * mm)
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


@dataclass
class Dimensions:
    """Element dimensions with units."""
    width: float
    height: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Tuple[float, float]:
        """Convert dimensions to points."""
        if self.unit == Units.POINTS:
            return (self.width, self.height)
        elif self.unit == Units.INCHES:
            return (self.width * inch, self.height * inch)
        elif self.unit == Units.CM:
            return (self.width * cm, self.height * cm)
        elif self.unit == Units.MM:
            return (self.width * mm, self.height * mm)
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


@dataclass
class Margins:
    """Page margins with units."""
    top: float
    bottom: float
    left: float
    right: float
    unit: Units = Units.POINTS
    
    def to_points(self) -> Dict[str, float]:
        """Convert margins to points."""
        if self.unit == Units.POINTS:
            return {
                'top': self.top,
                'bottom': self.bottom,
                'left': self.left,
                'right': self.right
            }
        elif self.unit == Units.INCHES:
            return {
                'top': self.top * inch,
                'bottom': self.bottom * inch,
                'left': self.left * inch,
                'right': self.right * inch
            }
        elif self.unit == Units.CM:
            return {
                'top': self.top * cm,
                'bottom': self.bottom * cm,
                'left': self.left * cm,
                'right': self.right * cm
            }
        elif self.unit == Units.MM:
            return {
                'top': self.top * mm,
                'bottom': self.bottom * mm,
                'left': self.left * mm,
                'right': self.right * mm
            }
        else:
            raise LayoutError(f"Unsupported unit: {self.unit}")


class LayoutManager:
    """
    Advanced layout management for PDF documents.
    
    Provides utilities for positioning, alignment, and responsive layouts.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize layout manager with configuration."""
        self.config = config
        self.default_page_size = config.get('page_size', A4)
        self.default_margins = Margins(
            top=2*cm, bottom=2*cm, left=2*cm, right=2*cm, unit=Units.POINTS
        )
    
    def create_grid(self,
                   rows: int,
                   cols: int,
                   page_size: Optional[Tuple[float, float]] = None,
                   margins: Optional[Margins] = None) -> 'GridLayout':
        """
        Create a grid layout system.
        
        Args:
            rows: Number of rows
            cols: Number of columns
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            GridLayout instance
        """
        if rows <= 0 or cols <= 0:
            raise ValidationError("Grid must have positive rows and columns")
            
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        
        return GridLayout(rows, cols, page_size, margins)
    
    def calculate_center_position(self,
                                 element_width: float,
                                 element_height: float,
                                 page_size: Optional[Tuple[float, float]] = None,
                                 margins: Optional[Margins] = None) -> Position:
        """
        Calculate center position for an element.
        
        Args:
            element_width: Element width in points
            element_height: Element height in points
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            Position for centering the element
        """
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        margin_dict = margins.to_points()
        
        available_width = page_size[0] - margin_dict['left'] - margin_dict['right']
        available_height = page_size[1] - margin_dict['top'] - margin_dict['bottom']
        
        center_x = margin_dict['left'] + (available_width - element_width) / 2
        center_y = margin_dict['bottom'] + (available_height - element_height) / 2
        
        return Position(center_x, center_y)
    
    def align_elements_horizontally(self,
                                   elements: List[Dict[str, Any]],
                                   alignment: Alignment,
                                   page_size: Optional[Tuple[float, float]] = None,
                                   margins: Optional[Margins] = None) -> List[Position]:
        """
        Align multiple elements horizontally.
        
        Args:
            elements: List of element dicts with 'width' key
            alignment: Horizontal alignment
            page_size: Optional page size override
            margins: Optional margins override
            
        Returns:
            List of positions for aligned elements
        """
        if not elements:
            return []
            
        page_size = page_size or self.default_page_size
        margins = margins or self.default_margins
        margin_dict = margins.to_points()
        
        available_width = page_size[0] - margin_dict['left'] - margin_dict['right']
        positions = []
        
        if alignment == Alignment.LEFT:
            current_x = margin_dict['left']
            for element in elements:
                positions.append(Position(current_x, 0))  # Y will be set separately
                current_x += element['width']
                
        elif alignment == Alignment.RIGHT:
            total_width = sum(element['width'] for element in elements)
            current_x = page_size[0] - margin_dict['right'] - total_width
            for element in elements:
                positions.append(Position(current_x, 0))
                current_x += element['width']
                
        elif alignment == Alignment.CENTER:
            total_width = sum(element['width'] for element in elements)
            start_x = margin_dict['left'] + (available_width - total_width) / 2
            current_x = start_x
            for element in elements:
                positions.append(Position(current_x, 0))
                current_x += element['width']
        
        return positions


class GridLayout:
    """
    Grid-based layout system for precise element positioning.
    """
    
    def __init__(self,
                 rows: int,
                 cols: int,
                 page_size: Tuple[float, float],
                 margins: Margins):
        """Initialize grid layout."""
        self.rows = rows
        self.cols = cols
        self.page_size = page_size
        self.margins = margins
        self.margin_dict = margins.to_points()
        
        # Calculate cell dimensions
        available_width = page_size[0] - self.margin_dict['left'] - self.margin_dict['right']
        available_height = page_size[1] - self.margin_dict['top'] - self.margin_dict['bottom']
        
        self.cell_width = available_width / cols
        self.cell_height = available_height / rows
    
    def get_cell_position(self, row: int, col: int) -> Position:
        """
        Get position for a specific grid cell.
        
        Args:
            row: Row index (0-based)
            col: Column index (0-based)
            
        Returns:
            Position of the cell's top-left corner
        """
        if row < 0 or row >= self.rows:
            raise ValidationError(f"Row {row} out of range (0-{self.rows-1})")
        if col < 0 or col >= self.cols:
            raise ValidationError(f"Column {col} out of range (0-{self.cols-1})")
        
        x = self.margin_dict['left'] + col * self.cell_width
        y = self.page_size[1] - self.margin_dict['top'] - (row + 1) * self.cell_height
        
        return Position(x, y)
    
    def get_cell_dimensions(self) -> Dimensions:
        """
        Get dimensions of a single grid cell.
        
        Returns:
            Dimensions of one cell
        """
        return Dimensions(self.cell_width, self.cell_height)
    
    def span_cells(self, start_row: int, start_col: int, 
                   row_span: int, col_span: int) -> Tuple[Position, Dimensions]:
        """
        Get position and dimensions for spanning multiple cells.
        
        Args:
            start_row: Starting row index
            start_col: Starting column index
            row_span: Number of rows to span
            col_span: Number of columns to span
            
        Returns:
            Tuple of (position, dimensions) for the spanned area
        """
        if start_row + row_span > self.rows:
            raise ValidationError("Row span exceeds grid bounds")
        if start_col + col_span > self.cols:
            raise ValidationError("Column span exceeds grid bounds")
        
        position = self.get_cell_position(start_row, start_col)
        dimensions = Dimensions(
            self.cell_width * col_span,
            self.cell_height * row_span
        )
        
        return position, dimensions
